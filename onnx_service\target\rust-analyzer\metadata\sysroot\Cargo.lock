# This file is automatically @generated by Car<PERSON>.
# It is not intended for manual editing.
version = 4

[[package]]
name = "addr2line"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfbe277e56a376000877090da837660b4427aad530e3028d44e0bffe4f89a1c1"
dependencies = [
 "compiler_builtins",
 "gimli",
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "adler2"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "512761e0bb2578dd7380c6baaa0f4ce03e84f95e960231d1dec8bf4d7d6e2627"
dependencies = [
 "compiler_builtins",
 "rustc-std-workspace-core",
]

[[package]]
name = "alloc"
version = "0.0.0"
dependencies = [
 "compiler_builtins",
 "core",
]

[[package]]
name = "allocator-api2"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "683d7910e743518b0e34f1186f92494becacb047c7b6bf616c96772180fef923"

[[package]]
name = "alloctests"
version = "0.0.0"
dependencies = [
 "rand",
 "rand_xorshift",
]

[[package]]
name = "cc"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1aeb932158bd710538c73702db6945cb68a8fb08c519e6e12706b94263b36db8"
dependencies = [
 "shlex",
]

[[package]]
name = "cfg-if"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"
dependencies = [
 "compiler_builtins",
 "rustc-std-workspace-core",
]

[[package]]
name = "compiler_builtins"
version = "0.1.152"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2153cf213eb259361567720ce55f6446f17acd0ccca87fb6dc05360578228a58"
dependencies = [
 "cc",
 "rustc-std-workspace-core",
]

[[package]]
name = "core"
version = "0.0.0"

[[package]]
name = "coretests"
version = "0.0.0"
dependencies = [
 "rand",
 "rand_xorshift",
]

[[package]]
name = "dlmalloc"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9b5e0d321d61de16390ed273b647ce51605b575916d3c25e6ddf27a1e140035"
dependencies = [
 "cfg-if",
 "compiler_builtins",
 "libc",
 "rustc-std-workspace-core",
 "windows-sys",
]

[[package]]
name = "fortanix-sgx-abi"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57cafc2274c10fab234f176b25903ce17e690fca7597090d50880e047a0389c5"
dependencies = [
 "compiler_builtins",
 "rustc-std-workspace-core",
]

[[package]]
name = "getopts"
version = "0.2.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "14dbbfd5c71d70241ecf9e6f13737f7b5ce823821063188d7e46c41d371eebd5"
dependencies = [
 "rustc-std-workspace-core",
 "rustc-std-workspace-std",
 "unicode-width",
]

[[package]]
name = "gimli"
version = "0.31.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07e28edb80900c19c28f1072f2e8aeca7fa06b23cd4169cefe1af5aa3260783f"
dependencies = [
 "compiler_builtins",
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "hashbrown"
version = "0.15.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf151400ff0baff5465007dd2f3e717f3fe502074ca563069ce3a6629d07b289"
dependencies = [
 "allocator-api2",
 "compiler_builtins",
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "hermit-abi"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbd780fe5cc30f81464441920d82ac8740e2e46b29a6fad543ddd075229ce37e"
dependencies = [
 "compiler_builtins",
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "libc"
version = "0.2.171"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c19937216e9d3aa9956d9bb8dfc0b0c8beb6058fc4f7a4dc4d850edf86a237d6"
dependencies = [
 "rustc-std-workspace-core",
]

[[package]]
name = "memchr"
version = "2.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "78ca9ab1a0babb1e7d5695e3530886289c18cf2f87ec19a575a0abdce112e3a3"
dependencies = [
 "compiler_builtins",
 "rustc-std-workspace-core",
]

[[package]]
name = "miniz_oxide"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8402cab7aefae129c6977bb0ff1b8fd9a04eb5b51efc50a70bea51cda0c7924"
dependencies = [
 "adler2",
 "compiler_builtins",
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "object"
version = "0.36.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62948e14d923ea95ea2c7c86c71013138b66525b86bdc08d2dcc262bdb497b87"
dependencies = [
 "compiler_builtins",
 "memchr",
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "panic_abort"
version = "0.0.0"
dependencies = [
 "alloc",
 "cfg-if",
 "compiler_builtins",
 "core",
 "libc",
]

[[package]]
name = "panic_unwind"
version = "0.0.0"
dependencies = [
 "alloc",
 "cfg-if",
 "compiler_builtins",
 "core",
 "libc",
 "unwind",
]

[[package]]
name = "proc-macro2"
version = "1.0.93"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60946a68e5f9d28b0dc1c21bb8a97ee7d018a8b322fa57838ba31cc878e22d99"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "proc_macro"
version = "0.0.0"
dependencies = [
 "core",
 "std",
]

[[package]]
name = "profiler_builtins"
version = "0.0.0"
dependencies = [
 "cc",
]

[[package]]
name = "quote"
version = "1.0.38"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e4dccaaaf89514f546c693ddc140f729f958c247918a13380cccc6078391acc"
dependencies = [
 "proc-macro2",
]

[[package]]
name = "r-efi"
version = "4.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9e935efc5854715dfc0a4c9ef18dc69dee0ec3bf9cc3ab740db831c0fdd86a3"
dependencies = [
 "compiler_builtins",
 "rustc-std-workspace-core",
]

[[package]]
name = "r-efi-alloc"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31d6f09fe2b6ad044bc3d2c34ce4979796581afd2f1ebc185837e02421e02fd7"
dependencies = [
 "compiler_builtins",
 "r-efi",
 "rustc-std-workspace-core",
]

[[package]]
name = "rand"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3779b94aeb87e8bd4e834cee3650289ee9e0d5677f976ecdb6d219e5f4f6cd94"
dependencies = [
 "rand_core",
 "zerocopy",
]

[[package]]
name = "rand_core"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b08f3c9802962f7e1b25113931d94f43ed9725bebc59db9d0c3e9a23b67e15ff"
dependencies = [
 "zerocopy",
]

[[package]]
name = "rand_xorshift"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "513962919efc330f829edb2535844d1b912b0fbe2ca165d613e4e8788bb05a5a"
dependencies = [
 "rand_core",
]

[[package]]
name = "rustc-demangle"
version = "0.1.24"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "719b953e2095829ee67db738b3bfa9fa368c94900df327b3f07fe6e794d2fe1f"
dependencies = [
 "compiler_builtins",
 "rustc-std-workspace-core",
]

[[package]]
name = "rustc-std-workspace-alloc"
version = "1.99.0"
dependencies = [
 "alloc",
]

[[package]]
name = "rustc-std-workspace-core"
version = "1.99.0"
dependencies = [
 "core",
]

[[package]]
name = "rustc-std-workspace-std"
version = "1.99.0"
dependencies = [
 "std",
]

[[package]]
name = "shlex"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fda2ff0d084019ba4d7c6f371c95d8fd75ce3524c3cb8fb653a3023f6323e64"

[[package]]
name = "std"
version = "0.0.0"
dependencies = [
 "addr2line",
 "alloc",
 "cfg-if",
 "compiler_builtins",
 "core",
 "dlmalloc",
 "fortanix-sgx-abi",
 "hashbrown",
 "hermit-abi",
 "libc",
 "miniz_oxide",
 "object",
 "panic_abort",
 "panic_unwind",
 "r-efi",
 "r-efi-alloc",
 "rand",
 "rand_xorshift",
 "rustc-demangle",
 "std_detect",
 "unwind",
 "wasi",
 "windows-targets 0.0.0",
]

[[package]]
name = "std_detect"
version = "0.1.5"
dependencies = [
 "cfg-if",
 "compiler_builtins",
 "libc",
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "syn"
version = "2.0.98"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "36147f1a48ae0ec2b5b3bc5b537d267457555a10dc06f3dbc8cb11ba3006d3b1"
dependencies = [
 "proc-macro2",
 "quote",
 "unicode-ident",
]

[[package]]
name = "sysroot"
version = "0.0.0"
dependencies = [
 "proc_macro",
 "profiler_builtins",
 "std",
 "test",
]

[[package]]
name = "test"
version = "0.0.0"
dependencies = [
 "core",
 "getopts",
 "libc",
 "std",
]

[[package]]
name = "unicode-ident"
version = "1.0.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a210d160f08b701c8721ba1c726c11662f877ea6b7094007e1ca9a1041945034"

[[package]]
name = "unicode-width"
version = "0.1.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7dd6e30e90baa6f72411720665d41d89b9a3d039dc45b8faea1ddd07f617f6af"
dependencies = [
 "compiler_builtins",
 "rustc-std-workspace-core",
 "rustc-std-workspace-std",
]

[[package]]
name = "unwind"
version = "0.0.0"
dependencies = [
 "cfg-if",
 "compiler_builtins",
 "core",
 "libc",
 "unwinding",
]

[[package]]
name = "unwinding"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51f06a05848f650946acef3bf525fe96612226b61f74ae23ffa4e98bfbb8ab3c"
dependencies = [
 "compiler_builtins",
 "gimli",
 "rustc-std-workspace-core",
]

[[package]]
name = "wasi"
version = "0.11.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c8d87e72b64a3b4db28d11ce29237c246188f4f51057d65a7eab63b7987e423"
dependencies = [
 "compiler_builtins",
 "rustc-std-workspace-alloc",
 "rustc-std-workspace-core",
]

[[package]]
name = "windows-sys"
version = "0.59.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e38bc4d79ed67fd075bcc251a1c39b32a1776bbe92e5bef1f0bf1f8c531853b"
dependencies = [
 "windows-targets 0.52.6",
]

[[package]]
name = "windows-targets"
version = "0.0.0"

[[package]]
name = "windows-targets"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b724f72796e036ab90c1021d4780d4d3d648aca59e491e6b98e725b84e99973"
dependencies = [
 "windows_aarch64_gnullvm",
 "windows_aarch64_msvc",
 "windows_i686_gnu",
 "windows_i686_gnullvm",
 "windows_i686_msvc",
 "windows_x86_64_gnu",
 "windows_x86_64_gnullvm",
 "windows_x86_64_msvc",
]

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32a4622180e7a0ec044bb555404c800bc9fd9ec262ec147edd5989ccd0c02cd3"

[[package]]
name = "windows_aarch64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09ec2a7bb152e2252b53fa7803150007879548bc709c039df7627cabbd05d469"

[[package]]
name = "windows_i686_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e9b5ad5ab802e97eb8e295ac6720e509ee4c243f69d781394014ebfe8bbfa0b"

[[package]]
name = "windows_i686_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0eee52d38c090b3caa76c563b86c3a4bd71ef1a819287c19d586d7334ae8ed66"

[[package]]
name = "windows_i686_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "240948bc05c5e7c6dabba28bf89d89ffce3e303022809e73deaefe4f6ec56c66"

[[package]]
name = "windows_x86_64_gnu"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "147a5c80aabfbf0c7d901cb5895d1de30ef2907eb21fbbab29ca94c5b08b1a78"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24d5b23dc417412679681396f2b49f3de8c1473deb516bd34410872eff51ed0d"

[[package]]
name = "windows_x86_64_msvc"
version = "0.52.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "589f6da84c646204747d1270a2a5661ea66ed1cced2631d546fdfb155959f9ec"

[[package]]
name = "zerocopy"
version = "0.8.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa91407dacce3a68c56de03abe2760159582b846c6a4acd2f456618087f12713"
dependencies = [
 "zerocopy-derive",
]

[[package]]
name = "zerocopy-derive"
version = "0.8.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06718a168365cad3d5ff0bb133aad346959a2074bd4a85c121255a11304a8626"
dependencies = [
 "proc-macro2",
 "quote",
 "syn",
]
